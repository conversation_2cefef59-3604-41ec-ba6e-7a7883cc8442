<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Editor - HTML/CSS Live Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 5px;
            padding: 5px;
            background-color: #1e1e1e;
        }

        .panel {
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
            background-color: #2d2d2d;
        }

        .panel-header {
            background-color: #404040;
            color: #fff;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #333;
        }

        .html-panel {
            grid-column: 1;
            grid-row: 1;
        }

        .css-panel {
            grid-column: 2;
            grid-row: 1;
        }

        .preview-panel {
            grid-column: 1 / 3;
            grid-row: 2;
        }

        textarea {
            width: 100%;
            height: calc(100% - 50px);
            background-color: #1e1e1e;
            color: #f8f8f2;
            border: none;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
        }

        textarea:focus {
            background-color: #252525;
        }

        iframe {
            width: 100%;
            height: calc(100% - 50px);
            border: none;
            background-color: white;
        }

        /* Syntax highlighting colors */
        .html-panel textarea {
            color: #e06c75;
        }

        .css-panel textarea {
            color: #61dafb;
        }

        /* Scrollbar styling */
        textarea::-webkit-scrollbar {
            width: 8px;
        }

        textarea::-webkit-scrollbar-track {
            background: #2d2d2d;
        }

        textarea::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        textarea::-webkit-scrollbar-thumb:hover {
            background: #777;
        }

        .panel-header .icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="panel html-panel">
        <div class="panel-header">
            <span class="icon">📄</span>HTML
        </div>
        <textarea id="htmlCode" placeholder="Nhập mã HTML của bạn ở đây..."><!DOCTYPE html>
<html>
<head>
    <title>Trang web của tôi</title>
</head>
<body>
    <h1>Xin chào!</h1>
    <p>Đây là một đoạn văn bản mẫu.</p>
    <div class="box">
        <p>Đây là một hộp có màu nền.</p>
    </div>
    <button onclick="alert('Xin chào!')">Nhấn vào đây</button>
</body>
</html></textarea>
    </div>

    <div class="panel css-panel">
        <div class="panel-header">
            <span class="icon">🎨</span>CSS
        </div>
        <textarea id="cssCode" placeholder="Nhập mã CSS của bạn ở đây...">body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.box {
    background-color: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

button {
    background-color: #008CBA;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #007B9A;
}</textarea>
    </div>

    <div class="panel preview-panel">
        <div class="panel-header">
            <span class="icon">👁️</span>Kết quả minh họa
        </div>
        <iframe id="preview"></iframe>
    </div>

    <script>
        const htmlCode = document.getElementById('htmlCode');
        const cssCode = document.getElementById('cssCode');
        const preview = document.getElementById('preview');

        function updatePreview() {
            const html = htmlCode.value;
            const css = cssCode.value;
            
            const previewContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <style>${css}</style>
                </head>
                <body>
                    ${html.replace(/<!DOCTYPE html>[\s\S]*?<body[^>]*>/i, '').replace(/<\/body>[\s\S]*?<\/html>/i, '')}
                </body>
                </html>
            `;
            
            preview.srcdoc = previewContent;
        }

        // Update preview when typing
        htmlCode.addEventListener('input', updatePreview);
        cssCode.addEventListener('input', updatePreview);

        // Initial preview update
        updatePreview();

        // Auto-resize textareas and add tab support
        function addTabSupport(textarea) {
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            });
        }

        addTabSupport(htmlCode);
        addTabSupport(cssCode);
    </script>
</body>
</html>
