<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Editor - HTML/CSS Live Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            height: 100vh;
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 5px;
            padding: 5px;
            background-color: #f5f5f5;
        }

        .panel {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .panel-header {
            background-color: #e9ecef;
            color: #333;
            padding: 10px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #ddd;
            position: relative;
        }

        .html-panel {
            grid-column: 1;
            grid-row: 1;
        }

        .css-panel {
            grid-column: 2;
            grid-row: 1;
        }

        .preview-panel {
            grid-column: 1 / 3;
            grid-row: 2;
        }

        textarea {
            width: 100%;
            height: calc(100% - 90px);
            background-color: #ffffff;
            color: #333;
            border: none;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
        }

        textarea:focus {
            background-color: #f8f9fa;
            box-shadow: inset 0 0 0 2px #007bff;
        }

        iframe {
            width: 100%;
            height: calc(100% - 90px);
            border: none;
            background-color: white;
        }

        /* Syntax highlighting colors */
        .html-panel textarea {
            color: #d73a49;
        }

        .css-panel textarea {
            color: #005cc5;
        }

        /* Scrollbar styling */
        textarea::-webkit-scrollbar {
            width: 8px;
        }

        textarea::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        textarea::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        textarea::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .update-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }

        .update-btn:hover {
            background-color: #0056b3;
        }

        .auto-update {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #666;
        }

        .auto-update input[type="checkbox"] {
            margin-right: 5px;
        }

        .panel-header .icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="panel html-panel">
        <div class="panel-header">
            <span class="icon">📄</span>HTML
            <label class="auto-update">
                <input type="checkbox" id="autoUpdate" checked> Tự động cập nhật
            </label>
        </div>
        <textarea id="htmlCode" placeholder="Nhập mã HTML của bạn ở đây..."><h1>Xin chào!</h1>
<p>Đây là một đoạn văn bản mẫu.</p>
<div class="box">
    <p>Đây là một hộp có màu nền.</p>
</div>
<button onclick="alert('Xin chào!')">Nhấn vào đây</button></textarea>
    </div>

    <div class="panel css-panel">
        <div class="panel-header">
            <span class="icon">🎨</span>CSS
        </div>
        <textarea id="cssCode" placeholder="Nhập mã CSS của bạn ở đây...">body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
}

.box {
    background-color: #4CAF50;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

button {
    background-color: #008CBA;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #007B9A;
}</textarea>
    </div>

    <div class="panel preview-panel">
        <div class="panel-header">
            <span class="icon">👁️</span>Kết quả minh họa
            <button class="update-btn" onclick="updatePreview()">Cập nhật</button>
        </div>
        <iframe id="preview"></iframe>
    </div>

    <script>
        const htmlCode = document.getElementById('htmlCode');
        const cssCode = document.getElementById('cssCode');
        const preview = document.getElementById('preview');
        const autoUpdate = document.getElementById('autoUpdate');

        function updatePreview() {
            const html = htmlCode.value;
            const css = cssCode.value;

            const previewContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <style>${css}</style>
                </head>
                <body>
                    ${html}
                </body>
                </html>
            `;

            preview.srcdoc = previewContent;
        }

        // Auto update functionality
        function handleAutoUpdate() {
            if (autoUpdate.checked) {
                updatePreview();
            }
        }

        // Update preview when typing (only if auto-update is enabled)
        htmlCode.addEventListener('input', handleAutoUpdate);
        cssCode.addEventListener('input', handleAutoUpdate);

        // Auto-update checkbox change handler
        autoUpdate.addEventListener('change', function() {
            if (this.checked) {
                updatePreview();
            }
        });

        // Initial preview update
        updatePreview();

        // Auto-resize textareas and add tab support
        function addTabSupport(textarea) {
            textarea.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            });
        }

        addTabSupport(htmlCode);
        addTabSupport(cssCode);
    </script>
</body>
</html>
